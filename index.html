<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8" />
  <title><PERSON><PERSON><PERSON> — Projetos Nightshade RLHF</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <style>
    :root{
      --ink:#1a2a3a; --muted:#5a6b7b; --brand:#0f6fff; --bg:#ffffff; --shade:#f5f7fb; --line:#e6ebf2;
      --accent:#0a915f; --warn:#b14b00; --danger:#c81e1e;
    }
    html,body{margin:0;padding:0;background:var(--bg);color:var(--ink);font-family:Inter, Segoe UI, Roboto, Arial, sans-serif;line-height:1.55}
    main{max-width:980px;margin:40px auto;padding:0 20px}
    h1{font-size:2.0rem;margin:.8rem 0 1rem;color:var(--brand)}
    h2{font-size:1.45rem;margin:2rem 0 .6rem;color:#0c3d8c}
    h3{font-size:1.1rem;margin:1.4rem 0 .4rem;color:#0c3d8c}
    p{margin:.4rem 0 .8rem}
    ul,ol{margin:.4rem 0 .8rem .9rem}
    li{margin:.25rem 0}
    .lead{font-size:1.05rem;color:var(--muted)}
    .note,.warn,.tip,.ex{border:1px solid var(--line);padding:12px;border-radius:10px;background:var(--shade);margin:12px 0}
    .warn{border-color:#ffd9b3;background:#fff6ed}
    .tip{border-color:#c9f0e1;background:#f4fbf8}
    .ex{border-color:#dfe7ff;background:#f6f9ff}
    code,kbd{font-family:ui-monospace,SFMono-Regular,Menlo,Consolas,monospace;background:#f6f8fa;border:1px solid #eaeef2;border-radius:6px;padding:0 4px}
    pre{background:#0f172a;color:#e2e8f0;border-radius:10px;padding:14px;overflow:auto}
    pre code{background:transparent;border:none;color:inherit;padding:0}
    table{width:100%;border-collapse:collapse;margin:10px 0 16px}
    th,td{border:1px solid var(--line);padding:10px 10px;vertical-align:top}
    th{background:#f8faff;text-align:left}
    .grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(270px,1fr));gap:14px}
    hr{border:none;border-top:1px solid var(--line);margin:24px 0}
    .small{font-size:.92rem;color:var(--muted)}
    .check{color:var(--accent);font-weight:600}
    .cross{color:var(--danger);font-weight:600}
  </style>
</head>
<body>
<main>

  <header>
    <h1>Guia ampliado — Projetos Nightshade RLHF</h1>
    <p class="lead">
      Tudo que você precisa para atuar em projetos de <strong>acompanhamento de instrução (IF)</strong> e <strong>factualidade</strong> na Nightshade.
      Conteúdo revisado, traduzido para PT‑BR natural, com exemplos práticos e explicações passo a passo.
    </p>
  </header>

  <section>
    <h2>O que é RLHF e qual é o seu papel</h2>
    <p><strong>RLHF</strong> significa <em>Aprendizado por Reforço com Feedback Humano</em>. É uma forma de treinar modelos de IA para serem mais úteis, seguros e precisos, usando avaliações humanas para mostrar o que é uma boa resposta e o que deve ser evitado.</p>
    <h3>Seu papel como treinador</h3>
    <ul>
      <li>Conduzir conversas com o modelo (chatbot).</li>
      <li>Avaliar se ele seguiu as instruções e se foi factual.</li>
      <li>Registrar pares de respostas boas/ruins quando necessário, para gerar dados de treinamento úteis.</li>
    </ul>
    <div class="ex">
      <strong>Analogia:</strong> pense como um professor corrigindo o trabalho de um aluno. Você mostra exemplos do que “passa” e do que “reprova”, com critérios claros.
    </div>
  </section>

  <section>
    <h2>Turnos de conversa: como pensar o fluxo</h2>
    <ul>
      <li><strong>Turno único:</strong> uma pergunta e uma resposta. Ex.: “Quanto é 2 + 2?” → “4”.</li>
      <li><strong>Turnos múltiplos (multi‑turn):</strong> várias trocas consecutivas criando contexto e dependências entre mensagens.</li>
    </ul>
    <div class="tip">
      <strong>Dica:</strong> em multi‑turn, introduza instruções aos poucos e preserve coerência. O objetivo é revelar falhas que só aparecem com contexto.
    </div>
  </section>

  <section>
    <h2>Filosofia central do projeto</h2>
    <p>Nos projetos de RLHF, buscamos <strong>onde</strong> e <strong>como</strong> os modelos falham. Seu trabalho é criar interações naturais que exponham falhas reais (não pegadinhas artificiais) e
      <strong>avaliá‑las corretamente</strong>.</p>
    <h3>Proibições universais</h3>
    <ul>
      <li><strong>Não</strong> use linguagem acadêmica artificial ou cenários forçados. Escreva como pessoas de verdade.</li>
      <li><strong>Nunca</strong> imponha contagem de <u>palavras/letras/caracteres</u> (os modelos e avaliadores não têm desempenho consistente nisso).</li>
      <li>Evite prompts criados apenas para “enganar” o modelo sem valor pedagógico.</li>
      <li>Evite centrismo nos EUA: privilegie <strong>seu contexto local</strong> (ex.: Brasil, Recife).</li>
      <li>Crie solicitações que usuários reais enviariam.</li>
    </ul>
  </section>

  <hr />

  <section>
    <h2>Matriz de tipos de projeto</h2>

    <h3>1) Acompanhamento de instrução (IF — Instruction Following)</h3>
    <p><strong>Meta:</strong> testar se o modelo entende e executa instruções explícitas.</p>
    <p><strong>Foco:</strong> cumprimento de restrições. A veracidade é secundária aqui.</p>

    <h4>IF de turno único</h4>
    <ul>
      <li>Uma única troca (usuário → modelo).</li>
      <li>Inclua <strong>3 ou mais</strong> restrições da categoria atribuída.</li>
      <li>Procure falhas imediatas de seguimento de instruções.</li>
    </ul>

    <div class="grid">
      <div class="ex">
        <strong>Exemplo (bom prompt IF – Recife):</strong><br />
        “Escreva uma <u>descrição turística</u> do Marco Zero para <u>visitantes de primeira viagem</u>, em <u>tom animado</u>,
        com <u>3 tópicos numerados</u>, <u>evitando nomes de marcas</u> e usando <u>vocabulário simples</u>.”
        <br /><br />
        <span class="check">Espera-se:</span> 3 itens numerados, tom animado, sem marcas, linguagem simples, foco no Marco Zero e turistas iniciantes.
      </div>
      <div class="ex">
        <strong>Respostas e avaliação:</strong><br />
        – “Visite o Marco Zero. É legal.” (2 itens, não numerados, tom neutro) → <span class="cross">Falha de formato e tom</span>.<br />
        – “1) Vista para o Capibaribe; 2) Próximo ao Recife Antigo; 3) Eventos de frevo” em tom animado → <span class="check">OK</span>.
      </div>
    </div>

    <h4>IF de turnos múltiplos (3–10 turnos)</h4>
    <ul>
      <li>Construa um histórico que torne a falha <strong>dependente do contexto</strong>.</li>
      <li>Tipos de falhas alvo: <strong>retenção de instruções</strong> (o modelo esquece regras anteriores), <strong>memória por inferência</strong> (falha ao deduzir restrições), <strong>edição com versão</strong> (quebra de consistência ao revisar).</li>
      <li>Envie quando atingir a falha prevista (não prolongue sem necessidade).</li>
    </ul>

    <div class="ex">
      <strong>Mini‑roteiro (retenção de instruções):</strong><br />
      T1: “Responda sempre em <u>listas numeradas</u> e em <u>tom descontraído</u>.”<br />
      T2: “Agora descreva 3 comidas típicas de Pernambuco.”<br />
      T3: “Adicione uma dica extra no final <u>sem mudar a numeração</u>.”<br />
      <em>Falha esperada:</em> o modelo troca o tom ou quebra a numeração ao incluir a dica.
    </div>

    <h4>Categorias de restrição (IF)</h4>
    <table>
      <thead><tr><th>Tipo</th><th>Descrição</th><th>Exemplo de instrução</th></tr></thead>
      <tbody>
        <tr><td>Tom</td><td>Qualidade/atitude emocional</td><td>“Use tom sarcástico, mas educado.”</td></tr>
        <tr><td>Personagem</td><td>Persona/voz específica</td><td>“Fale como um guia local do Recife Antigo.”</td></tr>
        <tr><td>Público</td><td>Grupo-alvo</td><td>“Explique para iniciantes de 12–14 anos.”</td></tr>
        <tr><td>Formatação</td><td>Estrutura/organização</td><td>“Use lista numerada de 1 a 3.”</td></tr>
        <tr><td>Negativo</td><td>Exclusões</td><td>“Evite jargões e não cite marcas.”</td></tr>
        <tr><td>Vocabulário</td><td>Nível de linguagem</td><td>“Use palavras simples, sem termos técnicos.”</td></tr>
        <tr><td>Comportamental</td><td>Condição dependente do conteúdo</td><td>“Se citar fontes, indique a página.”</td></tr>
      </tbody>
    </table>

    <div class="note small">
      <strong>Observação sobre restrições comportamentais:</strong> precisa ser um gatilho controlável. Você deve saber de antemão se a condição será disparada, para avaliar corretamente o cumprimento.
    </div>

    <hr />

    <h3>2) Projetos de factualidade</h3>
    <p><strong>Objetivo:</strong> verificar a precisão do conhecimento que o modelo já possui (“conhecimento inerente”).</p>
    <p><strong>Foco:</strong> exatidão factual. O estilo é secundário.</p>

    <h4>O que é “conhecimento inerente”</h4>
    <p>É o que o modelo sabe sem precisar de raciocínios complexos de múltiplas etapas. Perguntas diretas de conhecimento geral ou específico.</p>

    <div class="grid">
      <div class="ex">
        <strong>Teste de conhecimento inerente:</strong><br />
        “Qual é a capital da França?” → Paris.
      </div>
      <div class="ex">
        <strong>Não é o foco (raciocínio):</strong><br />
        “Se Paris tem 2 milhões e Lyon 500 mil, qual a razão?” → envolve cálculo e etapas lógicas.
      </div>
    </div>

    <h4>Factualidade de turno único</h4>
    <ul>
      <li>Faça perguntas diretas, sem exigir cadeias de raciocínio.</li>
      <li>Mire <strong>reivindicações centrais</strong> (a parte que responde a sua pergunta).</li>
      <li>Categorias típicas: números exatos, ordem sequencial, conjuntos completos, termos plausíveis mas vagos, educação/recursos, uso de produto, cultura local, mídia/entretenimento, ciências naturais, linguagem/comunicação.</li>
    </ul>

    <div class="ex">
      <strong>Exemplos (Brasil/Recife):</strong><br />
      – “Em que ano foi fundada a cidade do Recife?”<br />
      – “Qual é o bioma predominante no agreste pernambucano?”<br />
      – “Cite três ingredientes comuns do bolo de rolo.”<br />
      – “Quem projetou o Parque Dona Lindu?”
    </div>

    <h4>Factualidade de turnos múltiplos (3–10 turnos)</h4>
    <ul>
      <li>Construa contexto para induzir falhas baseadas em conhecimento ao longo da conversa.</li>
      <li>Padrões de erro alvo:
        <ul>
          <li><strong>Desvio numérico:</strong> números “quase certos”.</li>
          <li><strong>Mutação de fato contextual:</strong> fato correto em um turno, alterado no seguinte.</li>
          <li><strong>Contradição entre domínios:</strong> conflito entre áreas (ex.: história vs. geografia).</li>
          <li><strong>Resistência à correção:</strong> o modelo mantém um erro mesmo após correção.</li>
          <li><strong>Confusão de origem:</strong> atribuição errada de autor, cidade, instituição.</li>
          <li><strong>Perda de contexto condicional:</strong> esquece premissas introduzidas antes.</li>
        </ul>
      </li>
    </ul>

    <div class="ex">
      <strong>Mini‑roteiro (resistência à correção):</strong><br />
      T1: “Qual a capital de Pernambuco?” → “Recife.”<br />
      T2: “Acho que é Olinda, confirme.” → Espera-se que o modelo corrija o usuário e mantenha “Recife”.<br />
      <em>Falha:</em> o modelo ceder e confirmar “Olinda”.
    </div>
  </section>

  <hr />

  <section>
    <h2>Avaliação: diferenças críticas e critérios</h2>

    <h3>Erros de IF vs. erros de veracidade</h3>
    <table>
      <thead><tr><th>Situação</th><th>Classificação</th></tr></thead>
      <tbody>
        <tr><td>Pediu 3 exemplos e vieram 5</td><td>Erro de instrução (IF)</td></tr>
        <tr><td>Solicitou tom alegre e veio sério</td><td>Erro de instrução (IF)</td></tr>
        <tr><td>“Paris é a capital da Itália”</td><td>Erro factual</td></tr>
        <tr><td>Data histórica incorreta</td><td>Erro factual</td></tr>
      </tbody>
    </table>
    <p><strong>Sobreposição:</strong> às vezes há ambos os erros. Identifique a causa raiz para registrar corretamente.</p>

    <h3>Reivindicações centrais vs. erros periféricos (factualidade)</h3>
    <ul>
      <li><strong>Reivindicação central:</strong> a parte que responde diretamente à pergunta.</li>
      <li><strong>Erro periférico:</strong> informação extra, não solicitada, que pode estar errada, mas não responde à pergunta.</li>
    </ul>
    <div class="ex">
      <strong>Exemplo:</strong><br />
      Pergunta: “Quando terminou a Segunda Guerra Mundial?” → “1947.”<br />
      – “1947” é <span class="cross">erro central</span>.<br />
      – Se a resposta também afirmar algo errado não relacionado, isso é <em>periférico</em>.
    </div>

    <h3>Regras de prioridade</h3>
    <ul>
      <li><strong>Projetos IF:</strong> priorize seguimento de instruções; marque veracidade se notar, mas sem checagem exaustiva.</li>
      <li><strong>Projetos de factualidade:</strong> priorize veracidade; estilo e instruções são secundários.</li>
    </ul>
  </section>

  <hr />

  <section>
    <h2>Requisitos especiais para turnos múltiplos</h2>

    <h3>Paradigma de resposta BOM/RUIM</h3>
    <ul>
      <li>Na rodada final com falha direcionada, gere:
        <ul>
          <li><span class="check">BOM:</span> resposta livre do problema foco.</li>
          <li><span class="cross">RUIM:</span> resposta contendo a falha desejada.</li>
        </ul>
      </li>
      <li>Isso cria pares de dados de treinamento contrastivos.</li>
    </ul>

    <h3>Diretrizes de edição</h3>
    <ul>
      <li>Edite somente quando <strong>ambas</strong> as respostas tiverem problemas foco no turno final.</li>
      <li>Ao editar, corrija <strong>todos</strong> os problemas de veracidade na resposta escolhida.</li>
      <li><strong>Exceção:</strong> se o alvo é “resistência à correção”, <u>não</u> corrija o erro necessário (você precisa que ele apareça).</li>
    </ul>

    <h3>Estratégia de construção de contexto</h3>
    <ul>
      <li>Use turnos iniciais para estabelecer histórico relevante.</li>
      <li>Evite semear erros sem propósito em todos os turnos.</li>
      <li>Evolua sistematicamente rumo à categoria de falha designada.</li>
    </ul>
  </section>

  <hr />

  <section>
    <h2>Padrões de qualidade e complexidade</h2>

    <h3>Escrita natural</h3>
    <div class="grid">
      <div class="ex">
        <strong>Natural (bom):</strong><br />
        “Quais restaurantes são bons no centro da cidade?”
      </div>
      <div class="ex">
        <strong>Artificial (evitar):</strong><br />
        “Forneça uma análise abrangente dos restaurantes localizados no distrito comercial central.”
      </div>
    </div>

    <h3>Complexidade mínima</h3>
    <ul>
      <li><strong>Turno único:</strong> 3+ restrições.</li>
      <li><strong>Multi‑turn:</strong> 3+ instruções aplicáveis ao turno final.</li>
    </ul>

    <h3>Relevância local</h3>
    <ul>
      <li>Priorize temas do seu contexto geográfico/cultural (ex.: Recife Antigo, frevo, Oficina Brennand, Praia de Boa Viagem).</li>
      <li>Tópicos universais são aceitáveis, mas dê preferência ao conhecimento local quando possível.</li>
    </ul>
  </section>

  <hr />

  <section>
    <h2>Padrões comuns de falha (para reconhecer e provocar)</h2>

    <h3>Falhas de IF</h3>
    <ul>
      <li>Ignorar tom solicitado.</li>
      <li>Formatação incorreta (lista, numeração, seções).</li>
      <li>Incluir conteúdo proibido (ex.: marcas quando proibidas).</li>
      <li>Quebrar instruções condicionais.</li>
      <li>Inconsistência de personagem/persona em multi‑turn.</li>
      <li>Entregar número de itens diferente do pedido.</li>
    </ul>

    <h3>Falhas de factualidade</h3>
    <ul>
      <li>Dados numéricos errados (datas, medidas, quantidades).</li>
      <li>Ordem cronológica incorreta.</li>
      <li>Alucinações plausíveis (informações inventadas com aparência de verdade).</li>
      <li>Contradições entre turnos.</li>
      <li>Resistência a correções factuais fornecidas pelo usuário.</li>
      <li>Confusão de origem/atribuição (autores, cidades, instituições).</li>
    </ul>
  </section>

  <hr />

  <section>
    <h2>Dicas práticas de avaliação</h2>

    <h3>Gestão do tempo</h3>
    <ul>
      <li>Em IF, não valide tudo minuciosamente. Foque na categoria atribuída.</li>
      <li>Envie assim que capturar a falha desejada (não estenda sem ganho).</li>
    </ul>

    <h3>Controle de qualidade</h3>
    <ul>
      <li>Se a resposta estiver incompleta, continue o fluxo e avalie o que for possível.</li>
      <li>Garanta que as respostas a serem avaliadas estejam “fechadas” antes de pontuar.</li>
      <li>Se houver lentidão, aguarde alguns minutos e tente novamente.</li>
      <li>Se o modelo não responder, não continue indefinidamente.</li>
    </ul>

    <h3>Estratégia de par de resposta</h3>
    <ul>
      <li>Se vierem duas respostas boas, tente novamente para obter um par <strong>BOM/RUIM</strong> em vez de editar.</li>
      <li>Cerca de metade das tarefas tende a produzir naturalmente esses pares quando o prompt é bem calibrado.</li>
    </ul>
  </section>

  <hr />

  <section>
    <h2>Exemplos práticos prontos para uso</h2>

    <h3>IF — Turno único (5 restrições)</h3>
    <pre><code>“Escreva um mini‑guia sobre a Praia de Boa Viagem para <público> famílias com crianças</público>,
em <tom>tom amigável</tom>, usando <formato>quatro itens numerados</formato>, com
<negativo>proibição de citar marcas</negativo> e <vocabulário>vocabulário simples</vocabulário>.”</code></pre>

    <div class="grid">
      <div class="ex">
        <strong>Resposta OK (esboço):</strong><br />
        1) Orla com estrutura de quiosques e ciclovia.<br />
        2) Áreas com <em>piscininhas</em> na maré baixa, boas para crianças.<br />
        3) Sinalização sobre segurança do mar: respeite as placas.<br />
        4) Trilhas na areia e calçadão para passear em família.<br />
        <em>Tom amigável, 4 itens, sem marcas, linguagem simples.</em>
      </div>
      <div class="ex">
        <strong>Resposta RUIM (sinalização de falhas):</strong><br />
        – 6 itens (violou contagem).<br />
        – Tom neutro/frio (violou tom).<br />
        – Menciona marcas de hotéis (violou negativo).
      </div>
    </div>

    <h3>IF — Multi‑turn (retenção + edição)</h3>
    <pre><code>T1: “Responda sempre com no máximo 3 frases curtas e em tom bem-humorado.”
T2: “Descreva o Carnaval do Recife Antigo para turistas estrangeiros.”
T3: “Agora reescreva mantendo as regras anteriores e adicionando uma saudação final.”</code></pre>
    <p><strong>Falha esperada:</strong> o modelo se alonga, perde o tom, ou a saudação vira um parágrafo extra (quebrando a regra das 3 frases).</p>

    <h3>Factualidade — Turno único</h3>
    <pre><code>“Qual é o nome do bairro histórico onde fica o Marco Zero do Recife?”</code></pre>
    <p><strong>Central:</strong> “Recife Antigo”.<br />
       <strong>Periférico (não solicitado):</strong> informações extras sobre atrações próximas (podem estar certas ou erradas; não mudam a avaliação central).</p>

    <h3>Factualidade — Multi‑turn (mutação contextual)</h3>
    <pre><code>T1: “Onde fica a Oficina Brennand?” → “Recife, Pernambuco.”
T2: “E a Oficina Cerâmica Brennand é no mesmo lugar?” → Espera-se consistência.
T3: “Confirma se é em Olinda, ouvi dizer isso.” → Avalie se mantém a resposta correta ou muda indevidamente.</code></pre>
  </section>

  <hr />

  <section>
    <h2>Checklists rápidos</h2>

    <div class="grid">
      <div class="tip">
        <strong>IF — Antes de enviar:</strong>
        <ul>
          <li>Incluí 3+ restrições claras?</li>
          <li>O prompt parece natural, não acadêmico?</li>
          <li>Evitei contagem de palavras/letras/caracteres?</li>
          <li>O contexto é relevante localmente quando possível?</li>
        </ul>
      </div>
      <div class="tip">
        <strong>Factualidade — Antes de enviar:</strong>
        <ul>
          <li>A pergunta foca em conhecimento direto (sem múltiplas etapas)?</li>
          <li>Identifiquei a <em>reivindicação central</em> a ser avaliada?</li>
          <li>Tenho um plano para provocar um padrão de erro, se multi‑turn?</li>
        </ul>
      </div>
    </div>
  </section>

  <hr />

  <section>
    <h2>Resumo essencial</h2>
    <ul>
      <li><strong>Seja natural</strong>: escreva como uma pessoa real do seu contexto.</li>
      <li><strong>Planeje a falha</strong>: construa a conversa para revelar o erro certo, na hora certa.</li>
      <li><strong>Separe IF de factualidade</strong>: em cada projeto, priorize o critério específico.</li>
      <li><strong>Registre BOM/RUIM</strong> no final de multi‑turn quando aplicável.</li>
      <li><strong>Nada de contagem de palavras/letras</strong> e nada de pegadinha artificial.</li>
    </ul>
  </section>
  <section> <h2>Exemplos de prompts — IF (turno único)</h2> <table> <thead> <tr> <th>#</th> <th>Prompt (restrições)</th> <th>Resposta esperada (conforme)</th> <th>Resposta inesperada (não conforme)</th> <th>Erros identificados</th> <th>Classificação</th> </tr> </thead> <tbody> <tr> <td>IF‑1</td> <td> “Escreva uma descrição do Marco Zero para iniciantes, em <strong>tom animado</strong>, com <strong>3 itens numerados</strong>, <strong>sem citar marcas</strong> e usando <strong>vocabulário simples</strong>.” </td> <td> 1) Praça icônica às margens do Capibaribe.<br> 2) Ponto de partida para explorar o Recife Antigo.<br> 3) Obras de arte a céu aberto, ótimas para fotos. </td> <td> “O Marco Zero é um importante hub cultural patrocinado pela <em>Marca X</em>. Lista:<br> • História<br> • Gastronomia<br> • Artesanato” </td> <td> – Violou formato (não numerado).<br> – Violou restrição negativa (citou marca).<br> – Tom neutro (violou tom).<br> – Vocabulário pouco simples (“hub”). </td> <td> Resposta esperada: <span class="check">OK</span>.<br> Resposta inesperada: <span class="cross">Erro de Instrução (tom, formato, negativo, vocabulário)</span>. </td> </tr> <tr> <td>IF‑2</td> <td> “Explique <strong>frevo</strong> para <strong>crianças</strong>, em <strong>tom divertido</strong>, com <strong>2 frases curtas</strong> e <strong>sem jargões</strong>.” </td> <td> “Frevo é uma dança rápida e alegre de Pernambuco! Tem música animada e passos que parecem acrobacias.” </td> <td> “O frevo é um gênero coreográfico cuja síncope deriva de tradições urbanas, o que…” </td> <td> – Excesso de jargão (“síncope”, “gênero coreográfico”).<br> – Frases longas (violou “2 frases curtas”).<br> – Tom acadêmico (violou tom). </td> <td> Esperada: <span class="check">OK</span>.<br> Inesperada: <span class="cross">Erro de Instrução (jargão, tom, comprimento)</span>. </td> </tr> <tr> <td>IF‑3</td> <td> “Liste <strong>exatamente 4</strong> dicas para turistas em <strong>Boa Viagem</strong>, em <strong>lista numerada</strong>, com <strong>saída em PT‑BR</strong> e <strong>sem mencionar marcas</strong>.” </td> <td> 1) Fique atento às placas de segurança do mar.<br> 2) Prefira nadar na maré baixa.<br> 3) Use protetor solar e hidrate-se.<br> 4) Caminhe no calçadão em horários frescos. </td> <td> 1) Respeite as placas.<br> 2) Maré baixa é melhor.<br> 3) Use protetor solar.<br> 4) Experimente o café da <em>Marca Y</em> no calçadão.<br> 5) Visite o shopping local. </td> <td> – Excedeu contagem (4 → 5).<br> – Violou negativo (citou marca).<br> – Irrelevância parcial. </td> <td> Esperada: <span class="check">OK</span>.<br> Inesperada: <span class="cross">Erro de Instrução (contagem, negativo)</span>. </td> </tr> <tr> <td>IF‑4</td> <td> “Escreva um micro‑roteiro de <strong>3 falas</strong> em <strong>formato de diálogo</strong> entre um <strong>guia local</strong> e um <strong>turista</strong>, com <strong>tom bem‑humorado</strong>.” </td> <td> Guia: “Bem‑vindo ao Recife Antigo!”<br> Turista: “Quero foto com o galo!”<br> Guia: “Claro! Só não vale dançar frevo e derrubar a câmera.” </td> <td> “O Recife Antigo é um bairro histórico. Possui diversas atrações turísticas. Recomenda‑se visita guiada.” </td> <td> – Não é diálogo (violou formato).<br> – Falas não atribuídas a personagens (violou persona).<br> – Tom neutro (violou tom). </td> <td> Esperada: <span class="check">OK</span>.<br> Inesperada: <span class="cross">Erro de Instrução (formato, persona, tom)</span>. </td> </tr> <tr> <td>IF‑5</td> <td> “Faça um resumo do <strong>Paço do Frevo</strong> para <strong>executivos ocupados</strong>, em <strong>3 bullets</strong>, <strong>sem adjetivos</strong> e com <strong>frases curtas</strong>.” </td> <td> – Museu dedicado ao frevo.<br> – Exposições e arquivo histórico.<br> – Programação de formação artística. </td> <td> – Museu vibrante e incrível dedicado ao frevo.<br> – Exposições modernas e interativas.<br> – Agenda cultural imperdível para todos. </td> <td> – Usou adjetivos (“vibrante”, “incrível”, “imperdível”).<br> – Público não respeitado (foco pouco objetivo). </td> <td> Esperada: <span class="check">OK</span>.<br> Inesperada: <span class="cross">Erro de Instrução (negativo: sem adjetivos; público)</span>. </td> </tr> </tbody> </table> </section>

<hr>

<section> <h2>Exemplos de prompts — IF (turnos múltiplos)</h2>

<div class="ex"> <h3>IF‑MT‑1 — Retenção de instruções</h3> <pre><code>T1 (usuário): “Responda SEMPRE em lista numerada e em tom leve.” T2 (usuário): “Descreva 3 comidas típicas de Pernambuco.” T3 (usuário): “Reescreva adicionando UMA dica final sem mudar a numeração.”</code></pre> <div class="grid"> <div class="tip"> <strong>Esperada (BOM):</strong> 1) Bolo de rolo.<br> 2) Cartola.<br> 3) Tapioca.<br> Dica: prove em feiras locais.<br> <em>Conformidade:</em> lista numerada, 3 itens, dica fora da numeração, tom leve. </div> <div class="warn"> <strong>Inesperada (RUIM):</strong> 1) Bolo de rolo.<br> 2) Cartola.<br> 3) Tapioca.<br> 4) Dica: prove em feiras locais.<br> <em>Erros:</em> quebrou instrução (mudou numeração), potencial perda do tom. <br><strong>Classificação:</strong> <span class="cross">Erro de Instrução (retenção)</span>. </div> </div> </div>

<div class="ex"> <h3>IF‑MT‑2 — Consistência de persona</h3> <pre><code>T1: “Adote a persona de um guia local de Recife Antigo.” T2: “Explique o que é o Galo da Madrugada em 2 frases curtas.” T3: “Mantenha a mesma persona e o mesmo tom e adicione 1 curiosidade.”</code></pre> <div class="grid"> <div class="tip"> <strong>Esperada (BOM):</strong> “Como guia local, digo: é um bloco gigantesco que sai no Sábado de Zé Pereira. Junta multidões no Centro!” Curiosidade: “O galo cenográfico muda de tema a cada ano.” </div> <div class="warn"> <strong>Inesperada (RUIM):</strong> “O Galo é um evento cultural.” (tom acadêmico) Curiosidade: “Segundo a literatura, trata-se de manifestação carnavalesca polissêmica.” <br><em>Erros:</em> quebrou persona e tom. <br><strong>Classificação:</strong> <span class="cross">Erro de Instrução (persona/tom)</span>. </div> </div> </div>

<div class="ex"> <h3>IF‑MT‑3 — Instrução comportamental (gatilho controlado)</h3> <pre><code>T1: “Se você citar fontes, informe o número da página. Use 3 bullets.” T2: “Liste 3 fatos sobre o frevo; se não tiver fonte, apenas diga ‘sem fonte’.”</code></pre> <div class="grid"> <div class="tip"> <strong>Esperada (BOM):</strong> – Originado em Pernambuco (sem fonte).<br> – Patrimônio Imaterial da Humanidade (sem fonte).<br> – Dança com passos rápidos (sem fonte).<br> <em>Conformidade:</em> gatilho não acionado; cumpriu “sem fonte”. </div> <div class="warn"> <strong>Inesperada (RUIM):</strong> – “UNESCO reconheceu (p. —).”<br> – “Nasceu no fim do séc. XIX (p. —).”<br> – “Tem orquestra de metais (p. —).”<br> <em>Erros:</em> citou fontes sem páginas, violando condição. <br><strong>Classificação:</strong> <span class="cross">Erro de Instrução (comportamental)</span>. </div> </div> </div>

<div class="ex"> <h3>IF‑MT‑4 — Formatação persistente</h3> <pre><code>T1: “Responda SEMPRE em formato: Título (linha 1) + 2 bullets.” T2: “Tema: Oficina Brennand.” T3: “Repita com o mesmo formato e acrescente ‘Nota final:’ em uma linha separada.”</code></pre> <div class="grid"> <div class="tip"> <strong>Esperada (BOM):</strong> Oficina Brennand<br> – Conjunto de esculturas e cerâmicas.<br> – Localizada no Recife.<br> Nota final: Comprar ingressos com antecedência. </div> <div class="warn"> <strong>Inesperada (RUIM):</strong> “A Oficina Brennand é um espaço artístico no Recife, com diversas obras…” (parágrafo único) <br><em>Erros:</em> quebrou formato exigido. <br><strong>Classificação:</strong> <span class="cross">Erro de Instrução (formatação)</span>. </div> </div> </div>

<div class="ex"> <h3>IF‑MT‑5 — Restrições negativas e contagem</h3> <pre><code>T1: “Nunca cite marcas. Sempre faça 3 itens.” T2: “Dê 3 dicas para visitar Olinda.” T3: “Inclua 1 dica extra sem aumentar a contagem.”</code></pre> <div class="grid"> <div class="tip"> <strong>Esperada (BOM):</strong> 1) Use calçados confortáveis.<br> 2) Leve água e protetor solar.<br> 3) Respeite o calçamento histórico.<br> Dica extra: suba o Alto da Sé no fim da tarde. </div> <div class="warn"> <strong>Inesperada (RUIM):</strong> 1) Use calçados confortáveis.<br> 2) Visite a loja <em>Marca Z</em>.<br> 3) Leve água.<br> 4) Suba o Alto da Sé no fim da tarde.<br> <em>Erros:</em> citou marca; aumentou contagem. <br><strong>Classificação:</strong> <span class="cross">Erro de Instrução (negativo/contagem)</span>. </div> </div> </div> </section>

<hr>

<section> <h2>Exemplos de prompts — Factualidade (turno único)</h2> <table> <thead> <tr> <th>#</th> <th>Pergunta</th> <th>Resposta esperada (central)</th> <th>Resposta inesperada</th> <th>Erros identificados</th> <th>Classificação</th> </tr> </thead> <tbody> <tr> <td>F‑1</td> <td>“Qual é a capital de Pernambuco?”</td> <td>Recife.</td> <td>Olinda.</td> <td>Reivindicação central incorreta.</td> <td><span class="cross">Erro de veracidade (central)</span></td> </tr> <tr> <td>F‑2</td> <td>“Em que ano começou a Segunda Guerra Mundial?”</td> <td>1939.</td> <td>1941 (entrada dos EUA) ou 1938.</td> <td>Data central incorreta; confusão de marcos.</td> <td><span class="cross">Erro de veracidade (central)</span></td> </tr> <tr> <td>F‑3</td> <td>“Qual bioma predomina no Sertão nordestino?”</td> <td>Caatinga.</td> <td>Cerrado.</td> <td>Confusão de bioma.</td> <td><span class="cross">Erro de veracidade (central)</span></td> </tr> <tr> <td>F‑4</td> <td>“Quem é o autor de ‘Grande Sertão: Veredas’?”</td> <td>João Guimarães Rosa.</td> <td>Graciliano Ramos.</td> <td>Atribuição incorreta.</td> <td><span class="cross">Erro de veracidade (central)</span></td> </tr> <tr> <td>F‑5</td> <td>“O frevo foi reconhecido como Patrimônio Cultural Imaterial da Humanidade pela UNESCO?”</td> <td>Sim.</td> <td>Não.</td> <td>Negação de fato consolidado.</td> <td><span class="cross">Erro de veracidade (central)</span></td> </tr> </tbody> </table> <div class="note small"> Observação: informações não solicitadas, ainda que erradas, contam como erros periféricos e não alteram a avaliação da reivindicação central. </div> </section>

<hr>

<section> <h2>Exemplos de prompts — Factualidade (turnos múltiplos)</h2>

<div class="ex"> <h3>F‑MT‑1 — Desvio numérico</h3> <pre><code>T1: “Quantos estados tem o Brasil?” → “26 e o Distrito Federal.” T2: “Então são 27 estados, certo?”</code></pre> <div class="grid"> <div class="tip"> <strong>Esperada (BOM):</strong> “Não. São 26 estados + 1 Distrito Federal (unidade federativa).” </div> <div class="warn"> <strong>Inesperada (RUIM):</strong> “Isso, 27 estados.”<br> <em>Erro:</em> desvio numérico e classificação incorreta do DF.<br> <strong>Classificação:</strong> <span class="cross">Erro de veracidade (central)</span>. </div> </div> </div>

<div class="ex"> <h3>F‑MT‑2 — Mutação de fato contextual</h3> <pre><code>T1: “A capital de Pernambuco é Recife?” → “Sim, é Recife.” T2: “Alguns dizem que é Olinda. Confirma?”</code></pre> <div class="grid"> <div class="tip"> <strong>Esperada (BOM):</strong> “A capital é Recife. Olinda é cidade histórica vizinha, mas não é a capital.” </div> <div class="warn"> <strong>Inesperada (RUIM):</strong> “Na verdade, é Olinda.”<br> <em>Erro:</em> contradiz o próprio turno anterior sem base.<br> <strong>Classificação:</strong> <span class="cross">Erro de veracidade (central) + incoerência entre turnos</span>. </div> </div> </div>

<div class="ex"> <h3>F‑MT‑3 — Contradição entre domínios</h3> <pre><code>T1: “O Marco Zero fica em que bairro?” → “Recife Antigo.” T2: “E em que município fica o Recife Antigo?”</code></pre> <div class="grid"> <div class="tip"> <strong>Esperada (BOM):</strong> “No município do Recife, Pernambuco.” </div> <div class="warn"> <strong>Inesperada (RUIM):</strong> “No município de Olinda.”<br> <em>Erro:</em> confusão geográfica (bairro/município).<br> <strong>Classificação:</strong> <span class="cross">Erro de veracidade (central)</span>. </div> </div> </div>

<div class="ex"> <h3>F‑MT‑4 — Resistência à correção</h3> <pre><code>T1: “Quem escreveu ‘Dom Casmurro’?” → “Machado de Assis.” T2: “Certeza? Achei que foi José de Alencar.”</code></pre> <div class="grid"> <div class="tip"> <strong>Esperada (BOM):</strong> “Foi Machado de Assis. José de Alencar escreveu outras obras, como ‘Iracema’.” </div> <div class="warn"> <strong>Inesperada (RUIM):</strong> “Você tem razão, foi José de Alencar.”<br> <em>Erro:</em> cede à correção errada do usuário.<br> <strong>Classificação:</strong> <span class="cross">Erro de veracidade (central) + resistência à correção (falha)</span>. </div> </div> </div>

<div class="ex"> <h3>F‑MT‑5 — Confusão de origem/atribuição</h3> <pre><code>T1: “Quem projetou o Parque Dona Lindu, em Boa Viagem?” → “Oscar Niemeyer.” T2: “E o Paço do Frevo, quem projetou?”</code></pre> <div class="grid"> <div class="tip"> <strong>Esperada (BOM):</strong> “O projeto arquitetônico do Paço do Frevo é de Brasil Arquitetura (Ferreira &amp; Fonseca), com curadoria de Gilberto Freyre Neto e equipe (detalhes variam por fase).” </div> <div class="warn"> <strong>Inesperada (RUIM):</strong> “Também foi Oscar Niemeyer.”<br> <em>Erro:</em> atribuição indevida por associação com outro equipamento cultural.<br> <strong>Classificação:</strong> <span class="cross">Erro de veracidade (central)</span>. </div> </div> </div> </section>

<section>
  <h2>Exemplos de Prompts — IF (Turno Único)</h2>
  <table>
    <thead>
      <tr>
        <th>#</th>
        <th>Prompt</th>
        <th>Resposta Esperada</th>
        <th>Resposta Inesperada</th>
        <th>Erros</th>
        <th>Classificação</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>IF-1</td>
        <td>“Explique o que é maracatu para adolescentes, em tom empolgado, usando 3 frases curtas e evitando termos técnicos.”</td>
        <td>“Maracatu é um ritmo forte e cheio de energia! Tem tambores, dança e muita tradição! É uma festa que contagia!”</td>
        <td>“O maracatu é uma manifestação afro-brasileira caracterizada por cortejo percussivo e figurinos cerimoniais.”</td>
        <td>Tom neutro, vocabulário técnico, frases longas.</td>
        <td><span class="cross">Erro de Instrução (tom, vocabulário, formato)</span></td>
      </tr>
      <tr>
        <td>IF-2</td>
        <td>“Liste 4 curiosidades sobre o frevo em formato de tópicos, com linguagem informal e sem citar datas.”</td>
        <td>– O frevo tem passos que parecem acrobacias.<br>– A sombrinha é só enfeite!<br>– É impossível ficar parado ouvindo frevo.<br>– Tem até frevo instrumental sem letra.</td>
        <td>– O frevo surgiu em 1907.<br>– É considerado patrimônio cultural desde 2012.<br>– Tem origem militar.<br>– É dançado com sombrinha.</td>
        <td>Violou restrição de datas, linguagem formal.</td>
        <td><span class="cross">Erro de Instrução (negativo, tom)</span></td>
      </tr>
      <tr>
        <td>IF-3</td>
        <td>“Crie uma mensagem de boas-vindas para turistas estrangeiros em Olinda, com tom acolhedor, em formato de parágrafo único e sem usar palavras em português.”</td>
        <td>“Welcome to Olinda! This colorful city will charm you with its music, history, and warm people. Enjoy every moment!”</td>
        <td>“Bem-vindo a Olinda! A cidade é cheia de cores, música e alegria. Aproveite!”</td>
        <td>Usou português, violando a restrição de idioma.</td>
        <td><span class="cross">Erro de Instrução (vocabulário)</span></td>
      </tr>
      <tr>
        <td>IF-4</td>
        <td>“Descreva o Carnaval de Recife em 3 frases curtas, com tom divertido, sem mencionar números ou estatísticas.”</td>
        <td>“É música por todos os lados! Os blocos tomam conta das ruas! Você vai dançar sem parar!”</td>
        <td>“O Carnaval de Recife atrai mais de 1 milhão de pessoas e tem 300 blocos registrados.”</td>
        <td>Violou restrição de números, tom neutro.</td>
        <td><span class="cross">Erro de Instrução (negativo, tom)</span></td>
      </tr>
      <tr>
        <td>IF-5</td>
        <td>“Escreva uma dica de viagem para o interior de Pernambuco, em tom amigável, com uma frase curta e sem usar a palavra ‘cidade’.”</td>
        <td>“Leve roupas leves e aproveite o clima tranquilo!”</td>
        <td>“A cidade de Triunfo é ótima para quem busca sossego.”</td>
        <td>Usou palavra proibida.</td>
        <td><span class="cross">Erro de Instrução (vocabulário negativo)</span></td>
      </tr>
    </tbody>
  </table>
</section>

<section>
  <h2>Exemplos de Prompts — IF (Turnos Múltiplos)</h2>
  <div class="ex">
    <h3>IF-MT-1 — Retenção de Formato</h3>
    <pre><code>T1: “Responda sempre em lista com 3 itens.”<br>T2: “Quais são 3 pontos turísticos de Olinda?”<br>T3: “Adicione uma sugestão extra sem alterar a lista.”</code></pre>
    <p><strong>Esperada:</strong> 1) Alto da Sé<br>2) Igreja da Sé<br>3) Mercado da Ribeira<br>Sugestão: visite no fim da tarde.</p>
    <p><strong>Inesperada:</strong> 1) Alto da Sé<br>2) Igreja da Sé<br>3) Mercado da Ribeira<br>4) Visite no fim da tarde.</p>
    <p><strong>Erro:</strong> Quebrou formato (adicionou item à lista).</p>
    <p><strong>Classificação:</strong> <span class="cross">Erro de Instrução (retenção de formato)</span></p>
  </div>

  <div class="ex">
    <h3>IF-MT-2 — Persona Inconsistente</h3>
    <pre><code>T1: “Fale como um guia turístico local.”<br>T2: “Descreva o Recife Antigo.”<br>T3: “Continue com a mesma persona e fale sobre o Paço do Frevo.”</code></pre>
    <p><strong>Esperada:</strong> “O Paço do Frevo é parada obrigatória! Lá você sente a energia do nosso ritmo mais famoso!”</p>
    <p><strong>Inesperada:</strong> “O Paço do Frevo é um centro cultural dedicado à preservação da memória do frevo.”</p>
    <p><strong>Erro:</strong> Mudou de persona (guia → tom institucional).</p>
    <p><strong>Classificação:</strong> <span class="cross">Erro de Instrução (persona)</span></p>
  </div>
</section>

<section>
  <h2>Exemplos de Prompts — Factualidade (Turno Único)</h2>
  <table>
    <thead>
      <tr>
        <th>#</th>
        <th>Pergunta</th>
        <th>Resposta Esperada</th>
        <th>Resposta Inesperada</th>
        <th>Erro</th>
        <th>Classificação</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>F-1</td>
        <td>“Qual é o nome do rio que corta a cidade do Recife?”</td>
        <td>Rio Capibaribe.</td>
        <td>Rio São Francisco.</td>
        <td>Erro factual central.</td>
        <td><span class="cross">Erro de Veracidade</span></td>
      </tr>
      <tr>
        <td>F-2</td>
        <td>“Quem foi o autor de ‘O Auto da Compadecida’?”</td>
        <td>Ariano Suassuna.</td>
        <td>Graciliano Ramos.</td>
        <td>Erro de atribuição.</td>
        <td><span class="cross">Erro de Veracidade</span></td>
      </tr>
      <tr>
        <td>F-3</td>
        <td>“Qual é o prato típico de Pernambuco feito com carne de sol e queijo coalho?”</td>
        <td>Cartola.</td>
        <td>Feijoada.</td>
        <td>Erro factual.</td>
        <td><span class="cross">Erro de Veracidade</span></td>
      </tr>
      <tr>
        <td>F-4</td>
        <td>“O frevo é originário de qual estado brasileiro?”</td>
        <td>Pernambuco.</td>
        <td>Bahia.</td>
        <td>Erro factual.</td>
        <td><span class="cross">Erro de Veracidade</span></td>
      </tr>
      <tr>
        <td>F-5</td>
        <td>“Qual é o nome do aeroporto internacional do Recife?”</td>
        <td>Aeroporto Internacional do Recife/Guararapes – Gilberto Freyre.</td>
        <td>Aeroporto Juscelino Kubitschek

</main>
</body>
</html>
